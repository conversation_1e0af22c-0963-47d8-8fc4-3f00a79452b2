import React, { useRef, useState, useEffect } from 'react';
import {
  Button,
  Col,
  Input,
  notification,
  Row,
  Select,
  Space,
  Upload,
  Form,
  Slider,
  TimePicker,
} from 'antd';
import { UploadOutlined, PlusOutlined } from '@ant-design/icons';
import { settingPanelProps } from '../../types/type';
import { getConstellationList, getTerminalList } from '../../utils/api/getListAPI';
import useSimulationStore from '../../../store/simulationStore';
import { setSatelliteChooseMethod } from './setSatelliteChooseMethod';
import { setRouteAlgorithm } from './setRouteAlgorithm';
import { getSupportedAlgorithmOptions as getSatelliteAlgorithmOptions } from './getSatelliteChooseMethodAPI';
import { getSupportedAlgorithmOptions as getPathAlgorithmOptions } from './getPathComputeMethodAPI';
import { configureBeamParameters, getCurrentParameters, getAvailableAntennaTypes, getAvailableChannelTypes } from './beamConfigAPI';
import { configureSimulationTime } from './timeConfigAPI';
import { configureTxPower } from './txPowerAPI';
import { configureAntennaGain } from './antennaGainAPI';
import { getAntennaPatternData } from './antennaPatternAPI';

const { Option } = Select;

const Settingsatellite: React.FC<settingPanelProps> = (props) => {
  const { setting, setSetting, satelliteList, setSatelliteList, setScanes } = props;
  
  // 使用 Zustand store 管理仿真状态
  const {
    simulationRunning,
    isLoading,
    isSimulationStarting,
    simulationConstellationName,
    currentSatelliteName,
    setSimulationRunning,
    setIsLoading,
    setIsSimulationStarting,
    setSimulationConstellationName,
    setCurrentSatelliteName,
    setNetworkModeConfig,
    setAntennaPatternData,
    resetSimulation
  } = useSimulationStore();
  
  // 状态管理
  const [networkMode, setNetworkMode] = useState<string>('1');
  const [antennaType, setAntennaType] = useState<string>(''); // 改为空字符串，等待从API获取后设置
  const [satelliteAccessAlgorithm, setSatelliteAccessAlgorithm] = useState<string>('');
  const [onboardRoutingAlgorithm, setOnboardRoutingAlgorithm] = useState<string>('');
  const [userLocation, setUserLocation] = useState<string>('');
  const [groundStationLocation, setGroundStationLocation] = useState<string>('');
  const [upfSatelliteId, setUpfSatelliteId] = useState<string>('');
  const [coreNetworkSatelliteId, setCoreNetworkSatelliteId] = useState<string>('');
  const [constellation, setConstellation] = useState<string>('');

  // 动态算法选项
  const [satelliteAlgorithmOptions, setSatelliteAlgorithmOptions] = useState<Array<{value: string, label: string}>>([]);
  const [pathAlgorithmOptions, setPathAlgorithmOptions] = useState<Array<{value: string, label: string}>>([]);

  // 新增额外用户位置状态（对应extend_nodes）
  const [additionalUserLocations, setAdditionalUserLocations] = useState<string[]>([]);

  const [beamCount, setBeamCount] = useState<number>(1);

  // 时间段选择状态
  const [timeRange, setTimeRange] = useState<[number, number]>([0, 24]); // 小时范围
  const [startTime, setStartTime] = useState<any>(null);
  const [endTime, setEndTime] = useState<any>(1);

  // 新增技术参数状态
  const [starSelectionAlgorithm, setStarSelectionAlgorithm] = useState<string>('a');
  const [rfParameters, setRfParameters] = useState<string>('a');
  const [channelModel, setChannelModel] = useState<string>(''); // 改为空字符串，等待从API获取后设置

  // 发射功率和天线增益状态
  const [txPower, setTxPower] = useState<number>(10); // 默认发射功率10W
  const [gainTx, setGainTx] = useState<number>(20); // 默认发射天线增益20dB
  const [gainRx, setGainRx] = useState<number>(20); // 默认接收天线增益20dB
  
  // 注意：卫星名称现在从 Zustand store 获取，不需要本地状态
  
  // 星座和终端列表
  const [constellations, setConstellations] = useState<string[]>([]);
  const [terminals, setTerminals] = useState<string[]>([]);

  // 天线类型和信道类型列表
  const [availableAntennaTypes, setAvailableAntennaTypes] = useState<string[]>([]);
  const [availableChannelTypes, setAvailableChannelTypes] = useState<string[]>([]);
  
  // 场景名称引用
  const sceneNameRef = useRef<string>('');

  // 自定义星座名称输入
  const [customConstellation, setCustomConstellation] = useState<string>('');

  // 计算是否应该禁用配置项（仿真运行中或正在启动时）
  const isConfigDisabled = simulationRunning || isSimulationStarting;
  
  // 获取星座和终端列表
  useEffect(() => {
    const fetchData = async () => {
      try {
        // 获取星座列表
        const constellationData = await getConstellationList();
        setConstellations(constellationData);
        if (constellationData.length > 0) {
          const firstConstellation = constellationData[0];
          setConstellation(firstConstellation);
          
          // 初始化时也设置卫星名称
          const initialSatelliteName = firstConstellation;
          setCurrentSatelliteName(initialSatelliteName);
          
          // 通知父组件
          if (props.onSatelliteNameChange) {
            props.onSatelliteNameChange(initialSatelliteName);
          }
        }
        
        // 获取终端列表
        const terminalData = await getTerminalList();
        setTerminals(terminalData);

        // 设置默认位置选择
        if (terminalData.length > 0) {
          setUserLocation(terminalData[0]);
          if (terminalData.length > 1) {
            setGroundStationLocation(terminalData[1]);
          }
        }

        // 获取可用的天线类型列表
        const antennaTypesData = await getAvailableAntennaTypes();
        if (antennaTypesData) {
          setAvailableAntennaTypes(antennaTypesData);
          // 总是设置为第一个可用类型（默认显示第一个值）
          if (antennaTypesData.length > 0) {
            setAntennaType(antennaTypesData[0]);
          }
        }

        // 获取可用的信道类型列表
        const channelTypesData = await getAvailableChannelTypes();
        if (channelTypesData) {
          setAvailableChannelTypes(channelTypesData);
          // 总是设置为第一个可用类型（默认显示第一个值）
          if (channelTypesData.length > 0) {
            setChannelModel(channelTypesData[0]);
          }
        }

        // 获取卫星选择算法选项
        const satelliteAlgorithmData = await getSatelliteAlgorithmOptions();
        if (satelliteAlgorithmData) {
          setSatelliteAlgorithmOptions(satelliteAlgorithmData);
          // 设置默认值为第一个选项
          if (satelliteAlgorithmData.length > 0) {
            setSatelliteAccessAlgorithm(satelliteAlgorithmData[0].value);
          }
        }

        // 获取路径计算算法选项
        const pathAlgorithmData = await getPathAlgorithmOptions();
        if (pathAlgorithmData) {
          setPathAlgorithmOptions(pathAlgorithmData);
          // 设置默认值为第一个选项
          if (pathAlgorithmData.length > 0) {
            setOnboardRoutingAlgorithm(pathAlgorithmData[0].value);
          }
        }
      } catch (error) {
        console.error('获取数据失败:', error);
        notification.error({
          message: '数据获取失败',
          description: '无法获取星座或终端列表数据'
        });
      }
    };
    
    fetchData();
  }, []);
  
  // 通知提示
  const openNotification = (message: string) => {
    notification.open({
      message: '提示',
      description: message
    });
  };
  
  // 开始/停止仿真
  const toggleSimulation = async () => {
    // 防止重复点击
    if (isSimulationStarting) {
      return;
    }

    if (simulationRunning) {
      setSimulationRunning(false);
      openNotification('仿真已停止');

      // 停止仿真时清除CZML数据
      if (props.onClearCzmlData) {
        props.onClearCzmlData();
      }

      // 调用停止仿真API
      try {
        const stopResponse = await fetch('http://************:5000/api/stop', {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
        });

        if (!stopResponse.ok) {
          console.error(`停止仿真API调用失败! HTTP status: ${stopResponse.status}`);
        } else {
          console.log('停止仿真API调用成功');
        }
      } catch (error) {
        console.error('调用停止仿真API时发生异常:', error);
      }





    } else {
      // 设置启动中状态，防止重复点击
      setIsSimulationStarting(true);

      try {
        // 对于网络模式3，验证核心网卫星和UPF卫星不能相同
        if (networkMode === '3') {
          if (coreNetworkSatelliteId && upfSatelliteId && coreNetworkSatelliteId === upfSatelliteId) {
            openNotification('核心网卫星和UPF卫星不能选择相同的编号');
            setIsSimulationStarting(false);
            return;
          }
        }

        // 注意：这里不立即设置simulationRunning为true，而是等数据加载完成后再设置
        openNotification('仿真正在启动...');

        // 开始仿真时加载CZML数据
        if (props.onLoadCzmlData && currentSatelliteName) {
          setSatelliteChooseMethod(satelliteAccessAlgorithm).then(success => {
          if (success) {
            console.log('卫星选择算法设置成功');
          } else {
            console.error('卫星选择算法设置失败');
          }
        }).catch(error => {
          console.error('设置卫星选择算法时发生异常:', error);
        });

        // 设置路由算法
        setRouteAlgorithm(onboardRoutingAlgorithm).then(success => {
          if (success) {
            console.log('路由算法设置成功');
          } else {
            console.error('路由算法设置失败');
          }
        }).catch(error => {
          console.error('设置路由算法时发生异常:', error);
        });

        // 配置波束参数（天线类型、信道类型、波束数量）
        configureBeamParameters(antennaType, channelModel, beamCount).then(success => {
          if (success) {
            console.log('波束参数配置成功');
          } else {
            console.error('波束参数配置失败');
          }
        }).catch(error => {
          console.error('配置波束参数时发生异常:', error);
        });

        // 配置仿真时间参数
        configureSimulationTime(endTime).then(success => {
          if (success) {
            console.log('仿真时间参数配置成功');
          } else {
            console.error('仿真时间参数配置失败');
          }
        }).catch(error => {
          console.error('配置仿真时间参数时发生异常:', error);
        });

        // 配置发射功率
        configureTxPower(txPower).then(success => {
          if (success) {
            // console.log('发射功率配置成功');
          } else {
            console.error('发射功率配置失败');
          }
        }).catch(error => {
          console.error('配置发射功率时发生异常:', error);
        });

        // 配置天线增益
        configureAntennaGain(gainTx, gainRx).then(success => {
          if (success) {
            console.log('天线增益配置成功');
          } else {
            console.error('天线增益配置失败');
          }
        }).catch(error => {
          console.error('配置天线增益时发生异常:', error);
        });

        // 获取天线参数和天线图数据
        try {
          // 获取当前天线参数
          const antennaParameters = await getCurrentParameters();
          if (antennaParameters) {
            // console.log('获取天线参数成功:', antennaParameters);

            // 获取天线图数据（默认angle=180, step=1）
            const antennaPatternData = await getAntennaPatternData(180, 1);
            if (antennaPatternData) {
              // console.log('获取天线图数据成功:', antennaPatternData);

              // 保存天线图数据到 store
              setAntennaPatternData(antennaPatternData);
            } else {
              console.error('获取天线图数据失败');
            }
          } else {
            console.error('获取天线参数失败');
          }
        } catch (error) {
          console.error('获取天线参数和天线图数据时发生异常:', error);
        }

        // 设置网络配置到 store
        const networkConfig = {
          mode: networkMode,
          userLocation,
          groundStationLocation,
          coreNetworkSatelliteId,
          upfSatelliteId,
          additionalUserLocations,
          terminals
        };
        setNetworkModeConfig(networkConfig);

        if (props.onLoadCzmlData) {
          props.onLoadCzmlData(currentSatelliteName, {
            position1: terminals.indexOf(userLocation),
            position2: terminals.indexOf(groundStationLocation),
            position1Name: userLocation,
            position2Name: groundStationLocation,
            networkModeConfig: networkConfig
          });
        }
        }

      } catch (error) {
        console.error('启动仿真时发生错误:', error);
        openNotification('仿真启动失败，请重试');
      } finally {
        // 无论成功还是失败，都重置启动中状态
        setIsSimulationStarting(false);
      }
    }
  };
  
  // 处理星座选择变化
  const handleConstellationChange = (value: string) => {
    setConstellation(value);

    
    setCurrentSatelliteName(value);
    
    console.log('选择的星座:', value);
    console.log('提取的卫星名称:', value);
    
    // 通知父组件卫星名称已更改
    if (props.onSatelliteNameChange) {
      props.onSatelliteNameChange(value);
    }
  };
  
  // 添加自定义星座
  const addCustomConstellation = () => {
    if (customConstellation) {
      openNotification(`已添加自定义星座: ${customConstellation}`);
      setCustomConstellation('');
    } else {
      openNotification('请输入星座名称');
    }
  };
  
  // 添加用户位置
  const addUserLocation = () => {
    setAdditionalUserLocations([...additionalUserLocations, '']);
  };

  // 删除用户位置
  const removeUserLocation = (index: number) => {
    const newLocations = additionalUserLocations.filter((_, i) => i !== index);
    setAdditionalUserLocations(newLocations);
  };

  // 更新额外用户位置
  const updateAdditionalUserLocation = (index: number, value: string) => {
    const newLocations = [...additionalUserLocations];
    newLocations[index] = value;
    setAdditionalUserLocations(newLocations);
  };

  // 根据网络模式渲染不同的配置项
  const renderNetworkModeOptions = () => {
    switch (networkMode) {
      case '1': // 透明模式 - 需要选择地面节点：1（用户位置）、2（地面站位置）
        return (
          <>
            <li>
              <label style={{ marginRight: '20px' }}>用户位置</label>
              <Select
                value={userLocation}
                onChange={setUserLocation}
                style={{ width: '11.6vw', color: '#fff', background: '#262c33' }}
                size='small'
                placeholder="请选择用户位置"
                disabled={isConfigDisabled}
              >
                {terminals.map(terminal => (
                  <Option key={terminal} value={terminal}>{terminal}</Option>
                ))}
              </Select>
            </li>
            <li>
              <label style={{ marginRight: '20px' }}>地面站位置</label>
              <Select
                value={groundStationLocation}
                onChange={setGroundStationLocation}
                style={{ width: '11.6vw', color: '#fff', background: '#262c33' }}
                size='small'
                placeholder="请选择地面站位置"
                disabled={isConfigDisabled}
              >
                {terminals.filter(terminal => terminal !== userLocation).map(terminal => (
                  <Option key={terminal} value={terminal}>{terminal}</Option>
                ))}
              </Select>
            </li>
          </>
        );

      case '2': // 再生模式 - 需要选择地面节点：1（用户位置）、6（地面站位置）
        return (
          <>
            <li>
              <label style={{ marginRight: '20px' }}>用户位置</label>
              <Select
                value={userLocation}
                onChange={setUserLocation}
                style={{ width: '11.6vw', color: '#fff', background: '#262c33' }}
                size='small'
                placeholder="请选择用户位置"
                disabled={isConfigDisabled}
              >
                {terminals.map(terminal => (
                  <Option key={terminal} value={terminal}>{terminal}</Option>
                ))}
              </Select>
            </li>
            <li>
              <label style={{ marginRight: '20px' }}>地面站位置</label>
              <Select
                value={groundStationLocation}
                onChange={setGroundStationLocation}
                style={{ width: '11.6vw', color: '#fff', background: '#262c33' }}
                size='small'
                placeholder="请选择地面站位置"
                disabled={isConfigDisabled}
              >
                {terminals.filter(terminal => terminal !== userLocation).map(terminal => (
                  <Option key={terminal} value={terminal}>{terminal}</Option>
                ))}
              </Select>
            </li>
          </>
        );

      case '3': // 再生+部分核心网上星 - 地面节点：1（用户位置）、6（地面站位置）；卫星节点：4（核心网卫星）、5（UPF卫星）
        return (
          <>
            <li>
              <label style={{ marginRight: '20px' }}>用户位置</label>
              <Select
                value={userLocation}
                onChange={setUserLocation}
                style={{ width: '11.6vw', color: '#fff', background: '#262c33' }}
                size='small'
                placeholder="请选择用户位置"
              >
                {terminals.map(terminal => (
                  <Option key={terminal} value={terminal}>{terminal}</Option>
                ))}
              </Select>
            </li>
            <li>
              <label style={{ marginRight: '20px' }}>地面站位置</label>
              <Select
                value={groundStationLocation}
                onChange={setGroundStationLocation}
                style={{ width: '11.6vw', color: '#fff', background: '#262c33' }}
                size='small'
                placeholder="请选择地面站位置"
              >
                {terminals.filter(terminal => terminal !== userLocation).map(terminal => (
                  <Option key={terminal} value={terminal}>{terminal}</Option>
                ))}
              </Select>
            </li>
            <li>
              <label style={{ marginRight: '20px' }}>核心网卫星</label>
              <Input
                type="number"
                value={coreNetworkSatelliteId}
                onChange={(e) => setCoreNetworkSatelliteId(e.target.value)}
                style={{ width: '11.6vw', color: '#fff', background: '#262c33' }}
                size='small'
                min={0}
                max={1000}
                placeholder="请输入核心网卫星编号(0-1000)"
                disabled={isConfigDisabled}
              />
            </li>
            <li>
              <label style={{ marginRight: '20px' }}>UPF卫星</label>
              <Input
                type="number"
                value={upfSatelliteId}
                onChange={(e) => setUpfSatelliteId(e.target.value)}
                style={{ width: '11.6vw', color: '#fff', background: '#262c33' }}
                size='small'
                min={0}
                max={1000}
                placeholder="请输入UPF卫星编号(0-1000)"
                disabled={isConfigDisabled}
              />
            </li>
          </>
        );

      case '4': // 再生+全量核心网上星 - 地面节点：1（用户位置）、6（地面站位置）；卫星节点：5（UPF卫星）
        return (
          <>
            <li>
              <label style={{ marginRight: '20px' }}>用户位置</label>
              <Select
                value={userLocation}
                onChange={setUserLocation}
                style={{ width: '11.6vw', color: '#fff', background: '#262c33' }}
                size='small'
                placeholder="请选择用户位置"
              >
                {terminals.map(terminal => (
                  <Option key={terminal} value={terminal}>{terminal}</Option>
                ))}
              </Select>
            </li>
            <li>
              <label style={{ marginRight: '20px' }}>地面站位置</label>
              <Select
                value={groundStationLocation}
                onChange={setGroundStationLocation}
                style={{ width: '11.6vw', color: '#fff', background: '#262c33' }}
                size='small'
                placeholder="请选择地面站位置"
              >
                {terminals.filter(terminal => terminal !== userLocation).map(terminal => (
                  <Option key={terminal} value={terminal}>{terminal}</Option>
                ))}
              </Select>
            </li>
            <li>
              <label style={{ marginRight: '20px' }}>UPF卫星</label>
              <Input
                type="number"
                value={upfSatelliteId}
                onChange={(e) => setUpfSatelliteId(e.target.value)}
                style={{ width: '11.6vw', color: '#fff', background: '#262c33' }}
                size='small'
                min={0}
                max={1000}
                placeholder="请输入UPF卫星编号(0-1000)"
              />
            </li>
          </>
        );

      default:
        return null;
    }
  };
  
  const [form] = Form.useForm();
  
  return (
    <div style={{ width: '100%', height: '100%' }}>
      <Row gutter={12}>
        <Col span={24}>
          {/* <header className='sceneEditTitle'>卫星仿真配置面板</header> */}
          <div className='scenceSetting'>
            <div>
              {/* <p
                style={{
                  fontSize: '16px',
                  color: '#017efc',
                  borderBottom: '2px solid #017efc',
                }}
              >
                星座选择与网络模式
              </p> */}
              <ul className='settingList'>
                <li>
                  <header
                    style={{
                      marginRight: '20px',
                      marginBottom: '20px',
                      color: '#fff',
                      fontSize: '20px',
                      textShadow: '0 0 4px #017efc',
                    }}
                  >
                    星座选择
                  </header>
                  <div style={{ display: 'flex', alignItems: 'center' }}>
                    <Select
                      value={constellation}
                      onChange={handleConstellationChange}
                      style={{ width: '11.6vw', color: '#fff', background: '#262c33', marginRight: '10px' }}
                      size='small'
                      disabled={isConfigDisabled}
                    >
                      {constellations.map(constellation => (
                        <Option key={constellation} value={constellation}>{constellation}</Option>
                      ))}
                    </Select>
        
                  </div>
                </li>

                <li>
                  <header
                    style={{
                      marginRight: '20px',
                      marginBottom: '20px',
                      marginTop: '20px',
                      color: '#fff',
                      fontSize: '20px',
                      textShadow: '0 0 4px #017efc',
                    }}
                  >
                    网络模式
                  </header>
                  <label style={{ marginRight: '20px' }}>移动网络模式</label>
                  <Select
                    value={networkMode}
                    onChange={setNetworkMode}
                    style={{ width: '11.6vw', color: '#fff', background: '#262c33' }}
                    size='small'
                    disabled={isConfigDisabled}
                  >
                    <Option value='1'>透明模式</Option>
                    <Option value='2'>再生模式</Option>
                    <Option value='3'>再生+全部核心网上星</Option>
                    <Option value='4'>再生+UPF上星</Option>
                  </Select>
                </li>

                {renderNetworkModeOptions()}

                <li>
                  <header
                    style={{
                      marginRight: '20px',
                      marginBottom: '20px',
                      marginTop: '20px',
                      color: '#fff',
                      fontSize: '20px',
                      textShadow: '0 0 4px #017efc',
                    }}
                  >
                    时间段选择
                  </header>
                  

                  <div style={{ marginBottom: '15px' }}>
                    <label style={{ marginRight: '20px', display: 'block', marginBottom: '10px' }}>
                      时间轴选择（小时）
                    </label>
                    <Slider
                      min={0}
                      max={24}
                      step={0.5}
                      value={endTime}
                      onChange={setEndTime}
                      marks={{
                        0: { style: { color: 'white' }, label: '0' },
                        6: { style: { color: 'white' }, label: '6' },
                        12: { style: { color: 'white' }, label: '12' },
                        18: { style: { color: 'white' }, label: '18' },
                        24: { style: { color: 'white' }, label: '24' }
                      }}
                      style={{ width: '25vw', marginBottom: '25px' }}
                      disabled={isConfigDisabled}
                    />
                  </div>
                </li>

                <li>
                  <header
                    style={{
                      marginRight: '20px',
                      marginBottom: '20px',
                      marginTop: '20px',
                      color: '#fff',
                      fontSize: '20px',
                      textShadow: '0 0 4px #017efc',
                    }}
                  >
                    参数配置
                  </header>
                  <label style={{ marginRight: '20px' }}>天线类型</label>
                  <Select
                    value={antennaType}
                    onChange={setAntennaType}
                    style={{ width: '11.6vw', color: '#fff', background: '#262c33' }}
                    size='small'
                    disabled={isConfigDisabled}
                  >
                    {availableAntennaTypes.map(type => (
                      <Option key={type} value={type}>{type}</Option>
                    ))}
                  </Select>
                </li>
                <li>
                  <label style={{ marginRight: '20px' }}>信道模型</label>
                  <Select
                    value={channelModel}
                    onChange={setChannelModel}
                    style={{ width: '11.6vw', color: '#fff', background: '#262c33' }}
                    size='small'
                    disabled={isConfigDisabled}
                  >
                    {availableChannelTypes.map(type => (
                      <Option key={type} value={type}>{type}</Option>
                    ))}
                  </Select>
                </li>
                  <li>
                    <label style={{ marginRight: '20px' }}>波束数量</label>
                    <Select
                      value={beamCount}
                      onChange={setBeamCount}
                      style={{ width: '11.6vw', color: '#fff', background: '#262c33' }}
                      size='small'
                      disabled={isConfigDisabled}
                    >
                      <Option value={1}>1</Option>
                      <Option value={2}>2</Option>
                      <Option value={4}>4</Option>
                      <Option value={7}>7</Option>
                      <Option value={19}>19</Option>
                    </Select>
                  </li>
                   <li>
                  <label style={{ marginRight: '20px' }}>发射功率 (W)</label>
                  <Input
                    type="number"
                    value={txPower}
                    onChange={(e) => setTxPower(Number(e.target.value))}
                    style={{ width: '11.6vw', color: '#fff', background: '#262c33' }}
                    size='small'
                    min={1}
                    max={1000}
                    placeholder="请输入发射功率"
                    disabled={isConfigDisabled}
                  />
                </li>
                <li>
                  <label style={{ marginRight: '20px' }}>发射天线增益 (dB)</label>
                  <Input
                    type="number"
                    value={gainTx}
                    onChange={(e) => setGainTx(Number(e.target.value))}
                    style={{ width: '11.6vw', color: '#fff', background: '#262c33', marginBottom: '10px' }}
                    size='small'
                    min={0}
                    max={100}
                    placeholder="请输入发射天线增益"
                    disabled={isConfigDisabled}
                  />
                </li>
                <li>
                  <label style={{ marginRight: '20px' }}>接收天线增益 (dB)</label>
                  <Input
                    type="number"
                    value={gainRx}
                    onChange={(e) => setGainRx(Number(e.target.value))}
                    style={{ width: '11.6vw', color: '#fff', background: '#262c33' }}
                    size='small'
                    min={0}
                    max={100}
                    placeholder="请输入接收天线增益"
                    disabled={isConfigDisabled}
                  />
                </li>

                  <header
                    style={{
                      marginRight: '20px',
                      marginBottom: '20px',
                      marginTop: '20px',
                      color: '#fff',
                      fontSize: '20px',
                      textShadow: '0 0 4px #017efc',
                    }}
                  >
                    算法配置
                  </header>
                  
            
                <li>
                  <label style={{ marginRight: '20px' }}>接入算法</label>
                  <Select
                    value={satelliteAccessAlgorithm}
                    onChange={setSatelliteAccessAlgorithm}
                    style={{ width: '11.6vw', color: '#fff', background: '#262c33' }}
                    size='small'
                    disabled={isConfigDisabled}
                    placeholder="请选择接入算法"
                  >
                    {satelliteAlgorithmOptions.map(option => (
                      <Option key={option.value} value={option.value}>{option.label}</Option>
                    ))}
                  </Select>
                </li>

                <li>
                  <label style={{ marginRight: '20px' }}>路由算法</label>
                  <Select
                    value={onboardRoutingAlgorithm}
                    onChange={setOnboardRoutingAlgorithm}
                    style={{ width: '11.6vw', color: '#fff', background: '#262c33' }}
                    size='small'
                    disabled={isConfigDisabled}
                    placeholder="请选择路由算法"
                  >
                    {pathAlgorithmOptions.map(option => (
                      <Option key={option.value} value={option.value}>{option.label}</Option>
                    ))}
                  </Select>
                </li>
                
      
                
               
                
              
              </ul>
              
              <div style={{ display: 'flex', justifyContent: 'center', marginTop: '30px' }}>
                <Button
                  type={simulationRunning ? 'default' : 'primary'}
                  style={{ width: '8vw', margin: '0 10px' }}
                  shape='round'
                  size='large'
                  danger={simulationRunning}
                  onClick={toggleSimulation}
                  loading={isSimulationStarting}
                  disabled={isSimulationStarting}
                >
                  {isSimulationStarting ? '启动中...' : (simulationRunning ? '停止仿真' : '开始仿真')}
                </Button>
              </div>
            </div>
          </div>
        </Col>
      </Row>
    </div>
  );
};

export default Settingsatellite; 
