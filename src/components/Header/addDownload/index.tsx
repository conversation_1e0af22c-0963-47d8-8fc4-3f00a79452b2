import React, { useState } from 'react';
import { Modal, Tabs, Form, Input, Button, message } from 'antd';
import styles from './style.module.css';
import { exportSatelliteTrajectory, exportPathSatelliteExternalFull, exportGroundStationChannelData } from './exportAPI';

const { TabPane } = Tabs;

interface DataExportProps {
  visible: boolean;
  onClose: () => void;
  onSubmit?: (result: any) => void;
}

interface SatelliteTrajectoryData {
  satelliteIds: string;
}

interface GroundStationChannelData {
  groundStationNumber: string;
}

const DataExport: React.FC<DataExportProps> = ({ visible, onClose, onSubmit }) => {
  const [activeTab, setActiveTab] = useState<string>('satelliteTrajectory');
  const [satelliteTrajectoryForm] = Form.useForm<SatelliteTrajectoryData>();
  const [groundStationChannelForm] = Form.useForm<GroundStationChannelData>();

  // 处理卫星轨迹导出提交
  const handleSatelliteTrajectorySubmit = async (values: SatelliteTrajectoryData) => {
    try {
      message.loading('正在导出卫星轨迹数据...', 0);

      const result = await exportSatelliteTrajectory(values.satelliteIds);

      message.destroy(); // 清除loading消息

      if (result) {
        message.success('卫星轨迹数据导出成功');
        if (onSubmit) {
          onSubmit({
            type: 'satelliteTrajectory',
            success: true,
            data: values
          });
        }
        handleClose();
      } else {
        message.error('卫星轨迹数据导出失败');
      }
    } catch (error: any) {
      message.destroy();
      message.error(`导出失败: ${error.message}`);
      console.error('卫星轨迹导出错误:', error);
    }
  };

  // 处理星间链路卫星完整数据导出
  const handlePathSatelliteExternalFullExport = async () => {
    try {
      message.loading('正在导出星间链路卫星完整数据...', 0);

      const result = await exportPathSatelliteExternalFull();

      message.destroy(); // 清除loading消息

      if (result) {
        message.success('星间链路卫星完整数据导出成功');
        if (onSubmit) {
          onSubmit({
            type: 'pathSatelliteExternalFull',
            success: true,
            data: {}
          });
        }
        handleClose();
      } else {
        message.error('星间链路卫星完整数据导出失败');
      }
    } catch (error: any) {
      message.destroy();
      message.error(`导出失败: ${error.message}`);
      console.error('星间链路卫星完整数据导出错误:', error);
    }
  };

  // 处理地面站信道数据导出提交
  const handleGroundStationChannelSubmit = async (values: GroundStationChannelData) => {
    try {
      message.loading('正在导出地面站信道数据...', 0);

      const result = await exportGroundStationChannelData(values.groundStationNumber);

      message.destroy(); // 清除loading消息

      if (result) {
        message.success('地面站信道数据导出成功');
        if (onSubmit) {
          onSubmit({
            type: 'groundStationChannel',
            success: true,
            data: values
          });
        }
        handleClose();
      } else {
        message.error('地面站信道数据导出失败');
      }
    } catch (error: any) {
      message.destroy();
      message.error(`导出失败: ${error.message}`);
      console.error('地面站信道数据导出错误:', error);
    }
  };

  const handleClose = () => {
    satelliteTrajectoryForm.resetFields();
    groundStationChannelForm.resetFields();
    onClose();
  };

  return (
    <Modal
      title="数据导出"
      visible={visible}
      onCancel={handleClose}
      footer={null}
      width={600}
      centered
      className={styles.dataExportModal}
    >
      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane tab="卫星轨迹" key="satelliteTrajectory">
          <Form
            form={satelliteTrajectoryForm}
            layout="vertical"
            onFinish={handleSatelliteTrajectorySubmit}
            className={styles.form}
          >
            <Form.Item
              name="satelliteIds"
              label="卫星ID"
              rules={[
                { required: true, message: '请输入卫星ID' },
                {
                  validator: (_, value) => {
                    if (!value) return Promise.resolve();
                    
                    // 验证卫星ID格式：可以是单个ID或多个ID用逗号分隔
                    const ids = value.split(',').map((id: string) => id.trim());
                    const invalidIds = ids.filter((id: string) => !id || !/^[a-zA-Z0-9_-]+$/.test(id));
                    
                    if (invalidIds.length > 0) {
                      return Promise.reject(new Error('卫星ID格式不正确，只能包含字母、数字、下划线和连字符'));
                    }
                    
                    return Promise.resolve();
                  }
                }
              ]}
              extra="输入单个卫星ID或多个ID（用逗号分隔），例如：sat1 或 sat1,sat2,sat3"
            >
              <Input 
                placeholder="请输入卫星ID，多个ID用逗号分隔" 
                allowClear
              />
            </Form.Item>

            <Form.Item>
              <Button 
                type="primary" 
                htmlType="submit" 
                className={styles.submitButton}
              >
                导出卫星轨迹数据
              </Button>
            </Form.Item>
          </Form>
        </TabPane>

        <TabPane tab="星间链路卫星数据" key="pathSatelliteExternalFull">
          <div className={styles.form}>
            <div style={{ marginBottom: '20px' }}>
              <h4>星间链路卫星完整数据导出</h4>
              <p style={{ color: '#666', marginBottom: '20px' }}>
                导出所有时隙的星间链路卫星数据，包括：
              </p>
              <ul style={{ color: '#666', marginBottom: '20px', paddingLeft: '20px' }}>
                <li>时隙总数</li>
                <li>不重复卫星节点总数</li>
                <li>每个时隙的卫星ID列表和数量</li>
                <li>所有时隙索引的排序列表</li>
              </ul>
            </div>

            <Button
              type="primary"
              onClick={handlePathSatelliteExternalFullExport}
              className={styles.submitButton}
            >
              导出星间链路数据
            </Button>
          </div>
        </TabPane>

        <TabPane tab="地面站信道数据" key="groundStationChannel">
          <Form
            form={groundStationChannelForm}
            layout="vertical"
            onFinish={handleGroundStationChannelSubmit}
            className={styles.form}
          >
            <Form.Item
              name="groundStationNumber"
              label="地面站ID"
              rules={[
                { required: true, message: '请输入地面站数字ID' },
                {
                  validator: (_, value) => {
                    if (!value) return Promise.resolve();

                    // 验证地面站ID格式：必须是数字
                    if (!/^\d+$/.test(value.trim())) {
                      return Promise.reject(new Error('地面站ID必须为数字'));
                    }

                    return Promise.resolve();
                  }
                }
              ]}
              extra="输入地面站的数字ID，例如：0, 1, 2 等"
            >
              <Input
                placeholder="请输入地面站数字ID，例如：0"
                allowClear
              />
            </Form.Item>

            <div style={{ marginBottom: '20px' }}>
              <h4>导出内容说明：</h4>
              <ul style={{ color: '#666', paddingLeft: '20px' }}>
                <li>地面站ID（格式：数字）</li>
                <li>总时隙数</li>
                <li>时隙间隔（纳秒）</li>
                <li>总时长（纳秒）</li>
                <li>所有时隙的beam连接数据</li>
              </ul>
              <p style={{ color: '#999', fontSize: '12px', marginTop: '10px' }}>
                注意：输入数字ID后，系统会自动转换为 "Ground Station/数字" 格式发送给后端
              </p>
            </div>

            <Form.Item>
              <Button
                type="primary"
                htmlType="submit"
                className={styles.submitButton}
              >
                导出地面站信道数据
              </Button>
            </Form.Item>
          </Form>
        </TabPane>

        {/* 预留其他导出类型的Tab */}
        {/*
        <TabPane tab="通信数据" key="communicationData">
          <div style={{ padding: '20px', textAlign: 'center', color: '#888' }}>
            通信数据导出功能开发中...
          </div>
        </TabPane>

        <TabPane tab="网络拓扑" key="networkTopology">
          <div style={{ padding: '20px', textAlign: 'center', color: '#888' }}>
            网络拓扑导出功能开发中...
          </div>
        </TabPane>
        */}
      </Tabs>
    </Modal>
  );
};

export default DataExport;
