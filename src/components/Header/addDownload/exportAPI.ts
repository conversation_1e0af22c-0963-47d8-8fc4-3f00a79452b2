// 数据导出API - 调用 /export/satellite_trajectory 接口
import { notification } from 'antd';

// 卫星轨迹导出请求接口定义
export interface SatelliteTrajectoryExportRequest {
  satellite_ids: string;
}

// 卫星轨迹导出响应接口定义
export interface SatelliteTrajectoryExportResponse {
  success: boolean;
  message?: string;
  file_content?: string;
  filename?: string;
}

// 星间链路卫星数据响应接口定义
export interface PathSatelliteExternalFullResponse {
  total_slots: number;
  total_unique_satellites: number;
  slots_data: {
    [slotIndex: string]: {
      satellites: number[];
      satellite_count: number;
    };
  };
  slot_indices: number[];
}

// 地面站信道数据请求接口定义
export interface GroundStationChannelDataRequest {
  ground_station_id: string;
}

// 地面站信道数据响应接口定义
export interface GroundStationChannelDataResponse {
  success: boolean;
  data: {
    ground_station_id: string;
    total_slots: string;
    time_interval: string;
    duration: string;
    beam_connections: Array<{
      slot_index: string;
      time: string;
    }>;
  };
}

/**
 * 导出卫星轨迹数据
 * @param satelliteIds 卫星ID字符串，多个ID用逗号分隔
 * @returns Promise<boolean> 导出是否成功
 */
export const exportSatelliteTrajectory = async (satelliteIds: string): Promise<boolean> => {
  try {
    // 验证卫星ID不为空
    if (!satelliteIds.trim()) {
      console.error('卫星ID不能为空');
      notification.error({
        message: '导出失败',
        description: '卫星ID不能为空'
      });
      return false;
    }

    console.log(`导出卫星轨迹数据，卫星ID: ${satelliteIds}`);

    // 构建请求数据
    const requestData: SatelliteTrajectoryExportRequest = {
      satellite_ids: satelliteIds
    };

    // 调用后端API
    const response = await fetch('http://************:5001/api/export/satellite_trajectory', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestData)
    });

    if (!response.ok) {
      throw new Error(`HTTP错误! 状态: ${response.status}`);
    }

    // 检查响应类型
    const contentType = response.headers.get('content-type');
    
    if (contentType && contentType.includes('text/csv')) {
      // CSV文件响应
      const blob = await response.blob();
      const filename = response.headers.get('content-disposition')?.match(/filename="(.+)"/)?.[1] || 'satellite_trajectory.csv';
      
      // 创建下载链接
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = filename;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      window.URL.revokeObjectURL(url);

      console.log('卫星轨迹数据导出成功');
      notification.success({
        message: '导出成功',
        description: `卫星轨迹数据已导出为: ${filename}`
      });
      return true;
    } else {
      // JSON响应（错误情况）
      const result: SatelliteTrajectoryExportResponse = await response.json();
      
      if (result.success) {
        notification.success({
          message: '导出成功',
          description: result.message || '卫星轨迹数据导出成功'
        });
        return true;
      } else {
        notification.error({
          message: '导出失败',
          description: result.message || '服务器返回错误状态'
        });
        console.error('卫星轨迹数据导出失败:', result);
        return false;
      }
    }
  } catch (error: any) {
    console.error('导出卫星轨迹数据异常:', error);
    notification.error({
      message: '导出失败',
      description: `导出过程中发生错误: ${error.message}`
    });
    return false;
  }
};

/**
 * 导出星间链路卫星完整数据
 * @returns Promise<boolean> 导出是否成功
 */
export const exportPathSatelliteExternalFull = async (): Promise<boolean> => {
  try {
    console.log('开始导出星间链路卫星完整数据...');

    // 调用后端API
    const response = await fetch('http://************:5001/api/get_path_satellite_external_full', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      }
    });

    if (!response.ok) {
      throw new Error(`HTTP错误! 状态: ${response.status}`);
    }

    // 获取JSON数据
    const data: PathSatelliteExternalFullResponse = await response.json();

    // 创建JSON文件并下载
    const jsonString = JSON.stringify(data, null, 2);
    const blob = new Blob([jsonString], { type: 'application/json' });

    // 生成文件名（包含时间戳）
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, -5);
    const filename = `path_satellite_external_full_${timestamp}.json`;

    // 创建下载链接
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);

    console.log('星间链路卫星完整数据导出成功');
    notification.success({
      message: '导出成功',
      description: `星间链路卫星数据已导出为: ${filename}`
    });
    return true;
  } catch (error: any) {
    console.error('导出星间链路卫星完整数据异常:', error);
    notification.error({
      message: '导出失败',
      description: `导出过程中发生错误: ${error.message}`
    });
    return false;
  }
};

/**
 * 导出地面站信道数据
 * @param groundStationNumber 地面站数字ID
 * @returns Promise<boolean> 导出是否成功
 */
export const exportGroundStationChannelData = async (groundStationNumber: string): Promise<boolean> => {
  try {
    // 验证地面站数字ID不为空
    if (!groundStationNumber.trim()) {
      console.error('地面站数字ID不能为空');
      notification.error({
        message: '导出失败',
        description: '地面站数字ID不能为空'
      });
      return false;
    }

    // 验证输入是否为数字
    if (!/^\d+$/.test(groundStationNumber.trim())) {
      console.error('地面站ID必须为数字');
      notification.error({
        message: '导出失败',
        description: '地面站ID必须为数字'
      });
      return false;
    }

    // 构建完整的地面站ID
    // const fullGroundStationId = `Ground Station/${groundStationNumber.trim()}`;
    const fullGroundStationId = groundStationNumber.trim();
    

    console.log(`开始导出地面站信道数据，用户输入: ${groundStationNumber}, 完整ID: ${fullGroundStationId}`);

    // 构建请求数据
    const requestData: GroundStationChannelDataRequest = {
      ground_station_id: fullGroundStationId
    };

    // 调用后端API
    const response = await fetch('http://************:5001/api/get_all_channel_data', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestData)
    });

    if (!response.ok) {
      throw new Error(`HTTP错误! 状态: ${response.status}`);
    }

    // 获取JSON数据
    const result: GroundStationChannelDataResponse = await response.json();

    if (!result.success) {
      throw new Error('服务器返回失败状态');
    }

    // 创建JSON文件并下载
    const jsonString = JSON.stringify(result.data, null, 2);
    const blob = new Blob([jsonString], { type: 'application/json' });

    // 生成文件名（包含地面站数字ID和时间戳）
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, -5);
    const filename = `ground_station_channel_data_${groundStationNumber.trim()}_${timestamp}.json`;

    // 创建下载链接
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);

    console.log('地面站信道数据导出成功');
    notification.success({
      message: '导出成功',
      description: `地面站信道数据已导出为: ${filename}`
    });
    return true;
  } catch (error: any) {
    console.error('导出地面站信道数据异常:', error);
    notification.error({
      message: '导出失败',
      description: `导出过程中发生错误: ${error.message}`
    });
    return false;
  }
};
